package org.technoserve.udp.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * DTO for Cotton Farming Report combining Farmer and CottonFarmingData
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CottonFarmingReportDto {

    // Farmer fields
    private String farmerId;
    private String farmerTracenetCode;
    private String farmerName;
    private Integer age;
    private String gender;
    private String state;
    private String district;
    private String village;
    private String mobileNumber;
    private String centreId;
    private String centreName;
    private String centreType;
    private String maritalStatus;
    private String spouseName;
    private String caste;
    private String highestEducation;
    private Integer houseHoldSize;
    private Double landSizeUnderCultivation;
    private String landMeasureType;
    private String organicStatus;
    private Integer herdSize;
    private String anyOtherIncomeGeneratingActivity;
    private BigDecimal householdAnnualIncome;
    private BigDecimal agriculturalAnnualIncome;
    private BigDecimal dairyAnnualIncome;
    private BigDecimal otherAnnualIncome;
    private String cropsGrown;
    private String cattleBreedTypes;
    private BigDecimal loanAmount;
    private BigDecimal agriculturalLoan;
    private BigDecimal dairyLoan;
    private String latLong;

    // Program and Partner info
    private String partnerName;
    private String programName;

    // Cotton Farming Data fields
    private Integer year;
    private Integer malesInHousehold;
    private Integer femalesInHousehold;
    private Integer childrenInHousehold;
    private Integer schoolGoingChildren;
    private Integer earningMembers;
    private Double totalLandholding;
    private String primaryCrop;
    private String secondaryCrops;
    private Double nonOrganicCottonLand;
    private Double organicCottonLand;
    private Integer yearsOrganicPractice;
    private String certificationStatus;
    private String irrigationSource;
    private Integer cattleCount;
    private String drinkingWaterSource;
    private String preferredSellingPoint;
    private String hasStorageSpace;
    private String receivesAgroAdvisory;
    private String receivedTraining;
    private String membershipInOrg;
    private String maintainsRecords;
    private BigDecimal annualHouseholdIncome;
    private String primaryIncomeSource;
    private BigDecimal primaryIncomeAmount;
    private BigDecimal certificationCostPerAcre;
    private Double avgProductionPerAcre;
    private BigDecimal costOfCultivationPerAcre;
    private BigDecimal sellingPricePerKg;
    private BigDecimal totalProductionInKg; // Mapped from organicCottonQuantitySold
    private BigDecimal totalIncomeFromCotton; // Mapped from ocIncome
    private BigDecimal profitFromCotton; // Calculated field
    private String irrigationMethodUsed; // Mapped from irrigationMethod
    private Integer irrigationFrequency; // Converted from Double
    private BigDecimal irrigationCostPerAcre;
    private String seedVariety; // Mapped from organicCottonSeedVariety
    private BigDecimal seedCostPerAcre; // Not available in entity
    private String fertilizerUsed; // Mapped from bioFertilizerUsed
    private BigDecimal fertilizerCostPerAcre; // Not available in entity
    private String pesticideUsed; // Mapped from pestManagementBioInputs
    private BigDecimal pesticideCostPerAcre; // Not available in entity
    private String weedingMethod;
    private BigDecimal weedingCostPerAcre;
    private BigDecimal mulchingCostPerAcre;
    private Integer tillageCount;
    private BigDecimal tillageCostPerAcre;
    private String harvestingTime;
    private BigDecimal transportationCostPerKg;
    private Double rejectedQuantity;
    private String priceDiscoveryMechanism;
    private String paymentTransactionType;
    private Integer creditDays;
    private String govtSchemeAvailed;
    private String cropInsurance;
    private BigDecimal cropInsuranceCostPerAcre;
    private String hasKCC;
    private String hasActiveBankAccount;
    private String cropRotationUsed;
    private String rotationCrops;
    private String waterTrackingDevices;
    private Integer pumpCapacity;
    private String bufferZone;
    private String cropResidueUtilization;

    // Calculated fields
    private Double genderRatio;
    private Double schoolAttendanceRate;
    private Double totalCottonLand;
    private Double organicPercent;
    private Double landUsedForCotton;
    private Double incomePerEarner;
    private Double ocIncome;
    private Double profitPerAcre;
    private Double totalCertificationCost;
    private Double totalPTCost;
    private Double totalYSTCost;
    private Double totalBSTCost;
    private Double totalPestMgmtCost;
    private Double totalLabourCost;
    private Double machineryCostTotal;
    private Double totalIrrigationCost;
}
