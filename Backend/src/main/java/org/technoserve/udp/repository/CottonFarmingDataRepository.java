package org.technoserve.udp.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.technoserve.udp.entity.dataflow.CottonFarmingData;
import org.technoserve.udp.entity.dataflow.CottonFarmingDataId;

import java.util.Optional;

@Repository
public interface CottonFarmingDataRepository extends JpaRepository<CottonFarmingData, CottonFarmingDataId> {

    Optional<CottonFarmingData> findByFarmerIdAndProgramIdAndPartnerIdAndYear(String farmerId, Long programId, Long partnerId, Integer year);

    void deleteAllByExcelFileMetaData_ExcelFileMetaDataId(Long excelFileMetaDataId);
}
