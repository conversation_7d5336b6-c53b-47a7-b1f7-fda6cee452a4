package org.technoserve.udp.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.technoserve.udp.entity.dataflow.CottonFarmingData;
import org.technoserve.udp.entity.dataflow.CottonFarmingDataId;

import java.util.List;
import java.util.Optional;

@Repository
public interface CottonFarmingDataRepository extends JpaRepository<CottonFarmingData, CottonFarmingDataId> {

    Optional<CottonFarmingData> findByFarmerIdAndProgramIdAndPartnerIdAndYear(String farmerId, Long programId, Long partnerId, Integer year);

    void deleteAllByExcelFileMetaData_ExcelFileMetaDataId(Long excelFileMetaDataId);

    /**
     * Find cotton farming data by program ID, partner ID and years with pagination and sorting
     *
     * @param programId The program ID (optional)
     * @param partnerId The partner ID (optional)
     * @param years The list of years to filter by (optional)
     * @param pageable The pageable object for pagination and sorting
     * @return Page of cotton farming data
     */
    @Query("SELECT c FROM CottonFarmingData c WHERE " +
           "(:programId IS NULL OR c.programId = :programId) AND " +
           "(:partnerId IS NULL OR c.partnerId = :partnerId) AND " +
           "(:years IS NULL OR c.year IN :years)")
    Page<CottonFarmingData> findByProgramIdAndPartnerIdAndYears(
        @Param("programId") Long programId,
        @Param("partnerId") Long partnerId,
        @Param("years") List<Integer> years,
        Pageable pageable);

    /**
     * Find cotton farming data by program ID, partner ID and years without pagination
     *
     * @param programId The program ID (optional)
     * @param partnerId The partner ID (optional)
     * @param years The list of years to filter by (optional)
     * @return List of cotton farming data
     */
    @Query("SELECT c FROM CottonFarmingData c WHERE " +
           "(:programId IS NULL OR c.programId = :programId) AND " +
           "(:partnerId IS NULL OR c.partnerId = :partnerId) AND " +
           "(:years IS NULL OR c.year IN :years)")
    List<CottonFarmingData> findByProgramIdAndPartnerIdAndYears(
        @Param("programId") Long programId,
        @Param("partnerId") Long partnerId,
        @Param("years") List<Integer> years);
}
